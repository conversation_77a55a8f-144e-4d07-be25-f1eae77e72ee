package com.tti.oh_crm_service.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.codec.multipart.FilePart;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class ImageUtils {

    // Supported image file extensions
    private static final List<String> SUPPORTED_IMAGE_EXTENSIONS = Arrays.asList(
        ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp"
    );

    // Supported image content types
    private static final List<String> SUPPORTED_IMAGE_CONTENT_TYPES = Arrays.asList(
        "image/jpeg", "image/png", "image/gif", "image/webp", "image/bmp"
    );

    /**
     * Validates if the file is a supported image type
     * @param contentType The content type from the file part
     * @param fileName The original filename
     * @return true if the file is a valid image type
     */
    public static boolean isValidImageType(String contentType, String fileName) {
        // Check content type first
        if (contentType != null) {
            for (String supportedType : SUPPORTED_IMAGE_CONTENT_TYPES) {
                if (contentType.startsWith(supportedType)) {
                    return true;
                }
            }
        }
        
        // Fallback to file extension check
        if (fileName != null) {
            String lowerFileName = fileName.toLowerCase();
            return SUPPORTED_IMAGE_EXTENSIONS.stream()
                .anyMatch(lowerFileName::endsWith);
        }
        
        return false;
    }

    /**
     * Extracts the file extension from a filename
     * @param fileName The filename
     * @return The file extension including the dot, or ".jpg" as default
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return ".jpg"; // Default extension
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }
        return ".jpg"; // Default extension
    }

    /**
     * Extracts the base filename without extension and cleans it for file system compatibility
     * @param fileName The original filename
     * @return The cleaned base filename
     */
    public static String getBaseFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "image";
        }
        
        // Remove file extension
        int lastDotIndex = fileName.lastIndexOf('.');
        String baseName = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
        
        // Replace spaces and special characters with underscores for file system compatibility
        // Keep alphanumeric characters, dots, underscores, and hyphens
        return baseName.replaceAll("[^a-zA-Z0-9._-]", "_");
    }

    /**
     * Generates a unique filename using the format: originalName_timestamp.extension
     * @param originalFileName The original filename
     * @return A unique filename
     */
    public static String generateUniqueFileName(String originalFileName) {
        String fileExtension = getFileExtension(originalFileName);
        String baseFileName = getBaseFileName(originalFileName);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        
        return baseFileName + "_" + timestamp + fileExtension;
    }

    /**
     * Creates the uploads directory if it doesn't exist
     * @param uploadsPath The path to the uploads directory
     * @return true if directory exists or was created successfully
     */
    public static boolean ensureUploadsDirectoryExists(Path uploadsPath) {
        try {
            if (!Files.exists(uploadsPath)) {
                Files.createDirectories(uploadsPath);
                log.info("Created uploads directory: {}", uploadsPath);
            }
            return true;
        } catch (IOException e) {
            log.error("Failed to create uploads directory {}: {}", uploadsPath, e.getMessage());
            return false;
        }
    }

    /**
     * Creates the default uploads directory (uploads/) if it doesn't exist
     * @return true if directory exists or was created successfully
     */
    public static boolean ensureUploadsDirectoryExists() {
        return ensureUploadsDirectoryExists(Paths.get("uploads"));
    }

    /**
     * Saves a FilePart to the specified path using traditional I/O (no reactive operations)
     * @param filePart The file part to save
     * @param targetPath The target file path
     * @return The size of the saved file, or -1 if failed
     */
    public static long saveFilePart(FilePart filePart, Path targetPath) {
        try (OutputStream outputStream = Files.newOutputStream(targetPath,
                StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING)) {

            // Collect all data buffers and write them synchronously
            DataBuffer dataBuffer = DataBufferUtils.join(filePart.content()).block();
            if (dataBuffer != null) {
                try (InputStream inputStream = dataBuffer.asInputStream()) {
                    inputStream.transferTo(outputStream);
                } finally {
                    DataBufferUtils.release(dataBuffer);
                }
            }

            // Get and return file size
            long fileSize = Files.size(targetPath);
            log.debug("Successfully saved file: {} (size: {} bytes)", targetPath, fileSize);
            return fileSize;
        } catch (Exception e) {
            log.error("Failed to save file {}: {}", targetPath, e.getMessage());
            return -1L;
        }
    }

    /**
     * Validates the filename and returns error message if invalid
     * @param fileName The filename to validate
     * @return null if valid, error message if invalid
     */
    public static String validateFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "File has no name";
        }
        
        // Check for potentially dangerous characters
        if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
            return "File name contains invalid characters";
        }
        
        return null; // Valid
    }

    /**
     * Gets the content type from FilePart, with fallback to empty string
     * @param filePart The file part
     * @return The content type or empty string
     */
    public static String getContentType(FilePart filePart) {
        return filePart.headers().getContentType() != null ? 
            filePart.headers().getContentType().toString() : "";
    }

    /**
     * Formats file size in human-readable format
     * @param bytes The file size in bytes
     * @return Formatted file size string
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
