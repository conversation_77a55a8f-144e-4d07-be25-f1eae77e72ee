spring.application.name=oh-crm-service
management.endpoints.web.exposure.include=refresh

server.port=4003

eureka.instance.preferIpAddress=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.client.service-url.defaultZone=http://localhost:4000/eureka/

tti.security.jwt.secret-key=24dd6d4e44ed1832d6f60acc7dc92eb047647602bb5a1cb3642d43807b43efe0
# 1 day in millisecond
tti.security.jwt.expiration-time=86400000

spring.config.import=optional:configserver:

# File upload configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=50MB
spring.servlet.multipart.enabled=true

defaultTenant=org_1_db
# MYSQL Database config
spring.jpa.hibernate.ddl-auto=update
# spring.datasource.url=***************************************
# spring.datasource.username=ohcrm
# spring.datasource.password=tti_ohcrm_2024
# spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# spring.jpa.show-sql=true