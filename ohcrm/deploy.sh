#!/bin/bash

# Setup uploads directory permissions before deployment
echo "Setting up uploads directory permissions..."
if [ ! -d "./uploads" ]; then
    echo "Creating uploads directory..."
    mkdir -p ./uploads
fi

# Set proper ownership and permissions for uploads directory
# Get current user ID and group ID
USER_ID=$(id -u)
GROUP_ID=$(id -g)

echo "Setting ownership to $USER_ID:$GROUP_ID"
# Fix ownership to match container user (1000:1000)
if [ "$USER_ID" != "1000" ]; then
    echo "Warning: Your user ID is $USER_ID, but container expects 1000"
    echo "Attempting to set ownership to 1000:1000..."
    sudo chown -R 1000:1000 ./uploads 2>/dev/null || {
        echo "Could not set ownership to 1000:1000, trying current user..."
        chown -R $USER_ID:$GROUP_ID ./uploads 2>/dev/null || true
    }
else
    chown -R $USER_ID:$GROUP_ID ./uploads 2>/dev/null || true
fi

# Set proper permissions
chmod 755 ./uploads
if [ "$(ls -A ./uploads)" ]; then
    find ./uploads -type f -exec chmod 644 {} \;
    find ./uploads -type d -exec chmod 755 {} \;
fi

# Create .gitkeep if it doesn't exist
if [ ! -f "./uploads/.gitkeep" ]; then
    touch ./uploads/.gitkeep
    chmod 644 ./uploads/.gitkeep
fi

# Create a test file to verify volume mounting works
echo "test" > ./uploads/volume-test.txt
chmod 644 ./uploads/volume-test.txt

echo "Uploads directory permissions set successfully!"
echo "Directory contents:"
ls -la ./uploads/

# Test nginx configuration before deploying
echo ""
echo "Testing nginx configuration..."
docker run --rm -v "$(pwd)/nginx/proxy/nginx.conf:/etc/nginx/nginx.conf:ro" -v "$(pwd)/nginx/proxy/conf.d/:/etc/nginx/conf.d/" nginx nginx -t
if [ $? -ne 0 ]; then
    echo "❌ Nginx configuration test failed!"
    exit 1
fi
echo "✅ Nginx configuration is valid"

if [ $# -eq 0 ]
then
    docker ps -f name=services-discovery -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=api-gateway -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=tti-id-service -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=ohcrm-service -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=common-service -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=ohcrm-web -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=id-web -q | xargs --no-run-if-empty docker container stop
    docker ps -f name=nginx-proxy -q | xargs --no-run-if-empty docker container stop
    docker container ls -a -fname=services-discovery -q | xargs -r docker container rm
    docker container ls -a -fname=api-gateway -q | xargs -r docker container rm
    docker container ls -a -fname=tti-id-service -q | xargs -r docker container rm
    docker container ls -a -fname=ohcrm-service -q | xargs -r docker container rm
    docker container ls -a -fname=common-service -q | xargs -r docker container rm
    docker container ls -a -fname=ohcrm-web -q | xargs -r docker container rm
    docker container ls -a -fname=id-web -q | xargs -r docker container rm
    docker container ls -a -fname=nginx-proxy -q | xargs -r docker container rm
    docker rmi phhuan/tti:services-discovery-dev
    docker rmi phhuan/tti:api-gateway-dev
    docker rmi phhuan/tti:id-service-dev
    docker rmi phhuan/tti:ohcrm-service-dev
    docker rmi phhuan/tti:common-service-dev
    docker rmi phhuan/tti:ohcrm-web-dev
    docker rmi phhuan/tti:tti-id-web-dev
    docker compose up -d
else
    docker ps -f name=$1 -q | xargs --no-run-if-empty docker container stop
    docker container ls -a -fname=$1 -q | xargs -r docker container rm
    #docker rmi phhuan/tti:$1-dev
    docker compose up -d $1
fi
