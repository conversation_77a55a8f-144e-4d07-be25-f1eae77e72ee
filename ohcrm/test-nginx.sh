#!/bin/bash

echo "🧪 Testing nginx configuration..."
echo "================================"

# Test nginx configuration syntax
echo "📋 Testing nginx configuration syntax..."
docker run --rm -v "$(pwd)/nginx/proxy/nginx.conf:/etc/nginx/nginx.conf:ro" -v "$(pwd)/nginx/proxy/conf.d/:/etc/nginx/conf.d/" nginx nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx configuration syntax is valid"
else
    echo "❌ Nginx configuration syntax error"
    exit 1
fi

# Test if nginx can start with our configuration
echo ""
echo "🚀 Testing nginx startup..."
docker run --rm -d --name test-nginx -v "$(pwd)/nginx/proxy/nginx.conf:/etc/nginx/nginx.conf:ro" -v "$(pwd)/nginx/proxy/conf.d/:/etc/nginx/conf.d/" -v "$(pwd)/uploads/:/var/www/uploads/:ro" nginx

sleep 2

# Check if container is running
if docker ps | grep -q "test-nginx"; then
    echo "✅ Nginx started successfully"
    
    # Test if nginx is responding
    NGINX_IP=$(docker inspect test-nginx | grep '"IPAddress"' | head -1 | cut -d'"' -f4)
    if [ ! -z "$NGINX_IP" ]; then
        echo "🌐 Testing nginx response..."
        docker run --rm --link test-nginx:nginx alpine/curl -s -o /dev/null -w "%{http_code}" http://nginx/ | grep -q "200\|301\|302\|404"
        if [ $? -eq 0 ]; then
            echo "✅ Nginx is responding to requests"
        else
            echo "⚠️  Nginx is not responding properly"
        fi
    fi
    
    # Clean up
    docker stop test-nginx
else
    echo "❌ Nginx failed to start"
    docker logs test-nginx
    docker rm test-nginx
    exit 1
fi

echo ""
echo "🎉 Nginx configuration test completed successfully!"
echo "You can now run: ./deploy.sh"
