#!/bin/bash

echo "🔍 Debugging file sharing between containers..."
echo "=============================================="

# Check if both containers are running
echo "📋 Container Status:"
if docker ps | grep -q "ohcrm-service"; then
    echo "✅ ohcrm-service is running"
else
    echo "❌ ohcrm-service is not running"
    exit 1
fi

if docker ps | grep -q "nginx-proxy"; then
    echo "✅ nginx-proxy is running"
else
    echo "❌ nginx-proxy is not running"
    exit 1
fi

echo ""
echo "📁 Files in ohcrm-service container (/app/uploads/):"
docker exec ohcrm-service ls -la /app/uploads/

echo ""
echo "📁 Files in nginx-proxy container (/var/www/uploads/):"
docker exec nginx-proxy ls -la /var/www/uploads/

echo ""
echo "📁 Files on host (./uploads/):"
ls -la ./uploads/

echo ""
echo "🔍 Volume mount verification:"
echo "ohcrm-service mounts:"
docker inspect ohcrm-service | grep -A 10 -B 5 "uploads"

echo ""
echo "nginx-proxy mounts:"
docker inspect nginx-proxy | grep -A 10 -B 5 "uploads"

echo ""
echo "🧪 Testing file creation and sharing:"

# Create a test file from ohcrm-service
TEST_FILE="test-file-$(date +%s).txt"
echo "Creating test file from ohcrm-service: $TEST_FILE"
docker exec ohcrm-service sh -c "echo 'test from ohcrm-service' > /app/uploads/$TEST_FILE"

echo ""
echo "Checking if file appears in all locations:"

echo "1. In ohcrm-service:"
if docker exec ohcrm-service ls /app/uploads/$TEST_FILE >/dev/null 2>&1; then
    echo "✅ File exists in ohcrm-service"
else
    echo "❌ File NOT found in ohcrm-service"
fi

echo "2. In nginx-proxy:"
if docker exec nginx-proxy ls /var/www/uploads/$TEST_FILE >/dev/null 2>&1; then
    echo "✅ File exists in nginx-proxy"
else
    echo "❌ File NOT found in nginx-proxy (VOLUME MOUNT ISSUE!)"
fi

echo "3. On host:"
if [ -f "./uploads/$TEST_FILE" ]; then
    echo "✅ File exists on host"
else
    echo "❌ File NOT found on host"
fi

echo ""
echo "🔧 Testing nginx file serving:"
if docker exec nginx-proxy ls /var/www/uploads/$TEST_FILE >/dev/null 2>&1; then
    echo "Testing nginx access to the file..."
    docker exec nginx-proxy cat /var/www/uploads/$TEST_FILE
else
    echo "❌ Cannot test nginx serving - file not accessible to nginx"
fi

# Clean up test file
echo ""
echo "Cleaning up test file..."
docker exec ohcrm-service rm -f /app/uploads/$TEST_FILE 2>/dev/null
docker exec nginx-proxy rm -f /var/www/uploads/$TEST_FILE 2>/dev/null
rm -f ./uploads/$TEST_FILE 2>/dev/null

echo ""
echo "🎯 Diagnosis:"
if docker exec nginx-proxy ls /var/www/uploads/ | grep -q "corgi-png"; then
    echo "✅ Volume sharing works - nginx can see uploaded files"
    echo "   The 404 error might be a different issue"
else
    echo "❌ Volume sharing is BROKEN - nginx cannot see uploaded files"
    echo ""
    echo "🔧 Possible fixes:"
    echo "1. Check docker-compose.yml volume mounts"
    echo "2. Restart containers: docker-compose restart"
    echo "3. Recreate containers: docker-compose down && docker-compose up -d"
fi
