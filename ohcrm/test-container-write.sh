#!/bin/bash

echo "🧪 Testing container write permissions..."
echo "========================================"

# Check if container is running
if ! docker ps | grep -q "ohcrm-service"; then
    echo "❌ ohcrm-service container is not running"
    echo "   Run: docker-compose up -d ohcrm-service"
    exit 1
fi

echo "✅ Container is running"

# Check current ownership in container
echo ""
echo "📁 Current ownership in container:"
docker exec ohcrm-service ls -la /app/ | grep uploads

# Check what user the container is running as
echo ""
echo "👤 Container is running as:"
docker exec ohcrm-service whoami
docker exec ohcrm-service id

# Test write permissions
echo ""
echo "✍️  Testing write permissions..."
if docker exec ohcrm-service touch /app/uploads/test-write-$(date +%s).txt; then
    echo "✅ SUCCESS: Container can write to uploads directory!"
    echo "   Your upload API should work now"
    
    # Clean up test file
    docker exec ohcrm-service rm -f /app/uploads/test-write-*.txt
else
    echo "❌ FAILED: Container cannot write to uploads directory"
    echo ""
    echo "🔧 Try these fixes:"
    echo "1. Run: ./fix-ownership.sh"
    echo "2. Or manually: sudo chown -R 0:0 ./uploads"
    echo "3. Then restart: docker-compose restart ohcrm-service"
fi

echo ""
echo "📋 Directory contents in container:"
docker exec ohcrm-service ls -la /app/uploads/
