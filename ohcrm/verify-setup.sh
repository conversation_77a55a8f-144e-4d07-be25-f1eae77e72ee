#!/bin/bash

echo "🔍 Verifying OHCRM uploads directory setup..."
echo "================================================"

# Check if uploads directory exists
if [ -d "./uploads" ]; then
    echo "✅ Uploads directory exists"
else
    echo "❌ Uploads directory missing"
    exit 1
fi

# Check directory permissions
UPLOADS_PERMS=$(stat -f "%A" ./uploads 2>/dev/null || stat -c "%a" ./uploads 2>/dev/null)
if [ "$UPLOADS_PERMS" = "755" ]; then
    echo "✅ Uploads directory permissions correct (755)"
else
    echo "⚠️  Uploads directory permissions: $UPLOADS_PERMS (should be 755)"
fi

# Check ownership
UPLOADS_OWNER=$(stat -f "%u:%g" ./uploads 2>/dev/null || stat -c "%u:%g" ./uploads 2>/dev/null)
CURRENT_USER="$(id -u):$(id -g)"
if [ "$UPLOADS_OWNER" = "$CURRENT_USER" ]; then
    echo "✅ Uploads directory ownership correct ($UPLOADS_OWNER)"
else
    echo "⚠️  Uploads directory ownership: $UPLOADS_OWNER (current user: $CURRENT_USER)"
fi

# Check .gitkeep file
if [ -f "./uploads/.gitkeep" ]; then
    echo "✅ .gitkeep file exists"
else
    echo "⚠️  .gitkeep file missing"
fi

# Check docker-compose.yml configuration
if grep -q "./uploads/:/app/uploads/:rw" docker-compose.yml; then
    echo "✅ Docker compose ohcrm-service volume binding configured"
else
    echo "❌ Docker compose ohcrm-service volume binding missing"
fi

if grep -q "./uploads/:/var/www/uploads/:rw" docker-compose.yml; then
    echo "✅ Docker compose nginx-proxy volume binding configured"
else
    echo "❌ Docker compose nginx-proxy volume binding missing"
fi

if grep -q 'user: "1000:1000"' docker-compose.yml; then
    echo "✅ Docker compose user configuration found"
else
    echo "❌ Docker compose user configuration missing"
fi

# Check nginx configuration
if grep -q "location /uploads/" nginx/proxy/conf.d/default.conf; then
    echo "✅ Nginx uploads location configured"
else
    echo "❌ Nginx uploads location missing"
fi

if grep -q "alias /var/www/uploads/" nginx/proxy/conf.d/default.conf; then
    echo "✅ Nginx uploads alias configured"
else
    echo "❌ Nginx uploads alias missing"
fi

echo ""
echo "📁 Directory structure:"
ls -la ./uploads/

echo ""
echo "🐳 Docker configuration summary:"
echo "- ohcrm-service: ./uploads/ → /app/uploads/ (rw)"
echo "- nginx-proxy: ./uploads/ → /var/www/uploads/ (rw)"
echo "- Both containers run as user 1000:1000"

echo ""
echo "🌐 Access URLs:"
echo "- Upload API: POST http://api-dev.ttis.vn/crm/settings/images/upload"
echo "- Static files: GET http://api-dev.ttis.vn/uploads/<filename>"

echo ""
echo "🚀 Ready to deploy with: ./deploy.sh"
