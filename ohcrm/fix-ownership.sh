#!/bin/bash

echo "🔧 Fixing uploads directory ownership for root container..."
echo "========================================================="

# Check current ownership
echo "Current uploads directory ownership:"
ls -la ./uploads/

echo ""
echo "Container runs as root (no user directive), so setting ownership to root..."

# Fix ownership to root since container runs as root
sudo chown -R 0:0 ./uploads

if [ $? -eq 0 ]; then
    echo "✅ Successfully set ownership to root (0:0)"
else
    echo "❌ Failed to set ownership to root"
    echo "Trying alternative approach..."
    
    # Alternative: set very permissive permissions
    sudo chmod -R 777 ./uploads
    echo "Set permissions to 777 (read/write for everyone)"
fi

# Set proper permissions
echo ""
echo "Setting permissions..."
sudo chmod 755 ./uploads
sudo find ./uploads -type f -exec chmod 644 {} \; 2>/dev/null
sudo find ./uploads -type d -exec chmod 755 {} \; 2>/dev/null

echo ""
echo "Final ownership and permissions:"
ls -la ./uploads/

echo ""
echo "🚀 Now restart the ohcrm-service container:"
echo "   docker-compose restart ohcrm-service"
echo ""
echo "🧪 Check container ownership:"
echo "   docker exec ohcrm-service ls -la /app/"
echo ""
echo "Expected result: drwxr-xr-x    2 <USER>     <GROUP>          4096 ... uploads"
