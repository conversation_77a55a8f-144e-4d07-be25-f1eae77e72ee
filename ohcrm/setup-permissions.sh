#!/bin/bash

# Setup script for OHCRM uploads directory permissions
# This script ensures proper permissions for Docker containers

echo "Setting up uploads directory permissions..."

# Create uploads directory if it doesn't exist
if [ ! -d "./uploads" ]; then
    echo "Creating uploads directory..."
    mkdir -p ./uploads
fi

# Get current user ID and group ID
USER_ID=$(id -u)
GROUP_ID=$(id -g)

echo "Current user ID: $USER_ID"
echo "Current group ID: $GROUP_ID"

# Set ownership to current user (usually 1000:1000 on most systems)
echo "Setting ownership of uploads directory..."
sudo chown -R $USER_ID:$GROUP_ID ./uploads

# Set permissions: 
# - 755 for directory (rwxr-xr-x) - owner can read/write/execute, others can read/execute
# - 644 for files (rw-r--r--) - owner can read/write, others can read
echo "Setting directory permissions..."
chmod 755 ./uploads

# Set permissions for any existing files
if [ "$(ls -A ./uploads)" ]; then
    echo "Setting permissions for existing files..."
    find ./uploads -type f -exec chmod 644 {} \;
    find ./uploads -type d -exec chmod 755 {} \;
fi

# Create a .gitkeep file to ensure the directory is tracked in git
if [ ! -f "./uploads/.gitkeep" ]; then
    echo "Creating .gitkeep file..."
    touch ./uploads/.gitkeep
    chmod 644 ./uploads/.gitkeep
fi

echo "Permissions setup complete!"
echo ""
echo "Directory permissions:"
ls -la ./uploads
echo ""
echo "You can now run: docker-compose up -d"
